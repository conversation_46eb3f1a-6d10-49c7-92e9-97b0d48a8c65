<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>
<?php $this->load->helper('reports_datatable');
echo filters_dropdown(); ?>

<div id="no-data-template" style="display: none;">
    <?php echo no_data_message(); ?>
</div>


<div class="card-body" id="filtersContainer">
  <div class="d-flex align-items-end justify-content-between flex-wrap">
    <div class="d-flex flex-wrap gap-3">
      <div class="mb-2 me-3">
        <p class="label-text">Date Range</p>
        <div id="reportrange" class="dtrange w-100 ps-3 py-1">
          <span></span>
          <input type="hidden" id="from_date">
          <input type="hidden" id="to_date">
        </div>
      </div>
      <div class="form-group mb-2">
        <p class="label-text">Fee Type <font color="red">*</font>
        </p>
        <select class="form-control select" multiple title='Select' id="fee_type" name="fee_type">
          <?php foreach ($fee_blueprints as $key => $val) { ?>
            <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
          <?php } ?>
        </select>
        <div id="fee_type_error" style="color:#EE443F; font-size:13px; margin-top:16px;margin-left: 5.5px; display:none;"></div>
      </div>
    </div>
    <div class="mb-2">
      <input type="button" name="clear" id="clear" class="btn btn-outline-primary me-2" value="Clear">
      <input type="button" name="search" id="search" class="btn btn-primary" value="Get Report" style="margin-right:20px;">
    </div>
  </div>
</div>
<div class="progress" style="display:none">
  <div class="progress-bar" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
</div>

<style>
  .btn-group>.btn,
  .dtrange {
    background: #FFF;
  }

  .panel_heading_new_style_staff_border>.row {
    border-bottom: 0px !important;
  }

  .bootstrap-select>.btn,
  .dtrange {
    width: 269px !important;
    height: 40px !important;
  }

  .bootstrap-select.btn-group .dropdown-menu.inner {
    width: 269px;
  }

  .btn-default,
  .dtrange {
    border-color: #DEDCDF !important;
  }

  .bootstrap-select.form-control {
    border: 0px !important;
  }

  .bootstrap-select.btn-group .btn .caret {
    display: none !important;
  }

  .fee-type-error .bootstrap-select .dropdown-toggle,
  .fee-type-error select.form-control {
    background-color: #FDECEC !important;
    border-color: #EE443F !important;
    color: #EE443F !important;
  }

  .fee-type-error .bootstrap-select .dropdown-toggle .caret,
  .fee-type-error .bootstrap-select>.dropdown-toggle::after {
    color: #EE443F !important;
  }
</style>

<div class="panel-body">
  <div style="margin-top:-13px">
    <?php $this->load->helper('reports_datatable');
    echo render_search_buttons(); ?>
  </div>
  <div id="printArea">
    <div id="print_visible" style="display: none; text-align: center;">
      <h3 style="margin-bottom: 0.25rem; font-size: 1.5rem; font-family: 'Poppins', serif;">
        <?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?>
      </h3>
      <h4 style="margin-top: 0.25rem;font-size: 1.3rem;font-weight: bold;letter-spacing: 1px;color: #222;text-transform: uppercase;border-top: 1px solid #444;border-bottom: 1px solid #444;padding: 0.5rem 0;font-family: 'Poppins', serif;">
        Daily Fee Report
      </h4>
      <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
    </div>
    <div class="card-body daily_transcation_prarthana">
      <div id="loader" class="loaderclass d-none"></div>
    </div>
  </div>
</div>
</div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript">
  var chunks = [];

  function changeDateRange() {
    var range = $('#daterange').val();
    if (range == 7)
      $("#custom_range").show();
    else
      $("#custom_range").hide();
  }

  $(document).ready(function() {
    $('.date').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });
  });

  $('#search').on('click', function() {
    $('#loader').show();
    $('.prarthana_daily_reports').show();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var fee_type = $('#fee_type').val();

    $("#exportButtons").hide();
    $("#exportButtons2").hide();
    $('#fee_type').closest('.form-group').removeClass('fee-type-error');
    $('#fee_type_error').hide().text('');
    if (fee_type == null) {
      $('#fee_type_error').show().text('Select filter to proceed');
      $('#fee_type').closest('.form-group').addClass('fee-type-error');
      return false;
    }
    $('#fromDate').html(from_date);
    $('#toDate').html(to_date);
    $(".progress").show();
    $("#exportButtons").hide();
    $("#exportButtons2").hide();
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/generate_report_for_daily_prarthana_count'); ?>',
      type: 'post',
      data: {
        'from_date': from_date,
        'to_date': to_date,
        'fee_type': fee_type
      },
      success: function(data) {
        chunks = JSON.parse(data);
        // console.log(chunks);
        if (chunks.length == 0) {
          var noDataHtml = $('#no-data-template').html();
          $(".daily_transcation_prarthana").html(noDataHtml);
          $('.no-data-state').show();
          $(".progress").hide();
          $("#exportButtons").hide();
          $("#exportButtons2").hide();
        } else {
          student_ids = chunks;
          var html = '<table id="customers2" class="table table-bordered"><thead><tr><th>Sl.no</th><th>Student name</th><th>Father Name</th><th>Class</th><th>Section</th><th>Pay Type</th><th>DD NO</th><th>Bank Name</th><th>Amount</th></tr></thead><tbody id="report-data"></tbody></table>';
          $(".daily_transcation_prarthana").html(html);
          callReportGetter(0);
        }
      }
    });
  });

  function callReportGetter(index) {
    if (index < student_ids.length) {
      getReport(index);
    } else {
      $("#exportButtons").show();
      $("#exportButtons2").show();
      $(".progress").hide();
    }
  }

  function getReport(index) {
    var studentIds = student_ids[index];
    var fee_type = $('#fee_type').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/generate_report_for_daily_prarthana_new'); ?>',
      type: 'post',
      data: {
        'from_date': from_date,
        'to_date': to_date,
        'fee_type': fee_type,
        studentIds: studentIds
      },
      success: function(data) {
        var rData = JSON.parse(data);
        console.log(rData.length);
        if (rData.length == 0) {
          var noDataHtml = $('#no-data-template').html();
          $(".daily_transcation_prarthana").html(noDataHtml);
          $('.no-data-state').show();
        } else {
          prepare_circular_table(rData, index);
        }
        // $(".daily_transcation_prarthana").html(prepare_circular_table(rData));
      }
    });
  }

  function prepare_circular_table(rData, index) {
    var srNo = index * 150;
    var html = '';
    if (rData.length) {
      // html += '<table id="customers2" class="table datatable"><thead><tr><th>Sl.no</th><th>Student name</th><th>Father Name</th><th>Class</th><th>Section</th><th>Pay Type</th><th>DD NO</th><th>Bank Name</th><th>Amount</th></tr></thead><tbody>';
      for (i = 0, j = 0; i < rData.length; i++) {
        html += "<tr><td>" + (i + 1 + srNo) + "</td>";
        html += "<td>" + rData[i].student_name + "</td>";
        html += "<td>" + rData[i].father_name + "</td>";
        html += "<td>" + rData[i].class_name + "</td>";
        html += "<td>" + rData[i].section_name + "</td>";
        html += "<td>" + rData[i].paymentValue + "</td>";
        html += "<td>" + rData[i].cheque_dd_nb_cc_dd_number + "</td>";
        html += "<td>" + rData[i].bank_name + "</td>";
        html += "<td>" + rData[i].paid_amount + "</td>";
        html += '</tr>';
      }
      // html += '</tbody></table>';
      $("#report-data").append(html);
    }
    index++;
    callReportGetter(index);
  }


  function capitalizeFirstLetter(str) {
    str = str.toLowerCase().replace(/\b[a-z]/g, function(letter) {
      return letter.toUpperCase();
    });
    return str;
  }
</script>

<style type="text/css">
  table.fee_export_excel {
    box-sizing: border-box;
    border-collapse: collapse;
  }

  .fee_export_excel tr,
  .fee_export_excel td,
  .fee_export_excel th {
    border: 1px solid #ddd;
    position: relative;
    /*padding: 10px;*/
  }

  .vertical {
    padding: 10rem 0px !important;
  }

  .verticalTableHeader {
    text-align: center;
    /*white-space:none;*/
    /*  g-origin:50% 50%;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);*/
  }

  .verticalTableHeader p {
    margin: 0 -100%;
    display: inline-block;
    transform: rotate(-90deg);
    /*white-space: nowrap;*/

    bottom: 0;
    left: 50%;

  }

  .verticalTableHeader p:before {
    content: '';
    width: 0;
    padding-top: 110%;
    display: inline-block;
    vertical-align: middle;
  }

  .fee_export_excel th span {
    transform-origin: 0 50%;
    transform: rotate(-90deg);
    white-space: nowrap;
    display: block;
    position: absolute;
    bottom: 0;
    left: 50%;
  }

  .img-responsive {
    margin-left: 30px;
  }

  #search_sidebar {
    height: 23.5px
  }

  #academic_year {
    height: 23.5px;
    margin-top: 11px;
  }
</style>

<script>
  function printProfile() {
    const printWindow = window.open('', '_blank');
    let printHeader = document.getElementById('print_visible').outerHTML;
    printHeader = printHeader.replace('display: none;', ''); // Remove the inline style

    // Get the main report table (if exists)
    let mainTable = '';
    const tableElem = document.getElementById('customers2');
    if (tableElem) {
      mainTable = tableElem.outerHTML;
    } else {
      // fallback: get the report area html
      mainTable = document.querySelector('.daily_transcation_prarthana').innerHTML;
    }

    printWindow.document.write(`
        <html>
        <head>
            <title>Daily Fee Report</title>
            <style>
                body {
                    font-family: 'Inter', sans-serif;
                    padding: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    font-size: 12px;
                }
                h3 { margin: 15px 0; }
                @media print {
                    table { page-break-inside: auto }
                    tr { page-break-inside: avoid }
                }
            </style>
        </head>
        <body>
            ${printHeader}
            ${mainTable}
            <script>
            window.onload = function() {
                window.print();
            };
            window.onafterprint = function() {
                window.close();
            };
        <\/script>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
  }

  function exportToExcel_daily() {
    // Get the summary/header section
    let summaryHtml = document.getElementById('print_visible').outerHTML.replace('display: none;', '');

    // Get the main report table (if exists)
    let mainTable = '';
    const tableElem = document.getElementById('customers2');
    if (tableElem) {
      mainTable = tableElem.outerHTML;
    } else {
      mainTable = document.querySelector('.daily_transcation_prarthana').innerHTML;
    }

    // Excel-compatible HTML template with basic styling
    const excelHtml = `
        <html xmlns:o="urn:schemas-microsoft-com:office:office"
              xmlns:x="urn:schemas-microsoft-com:office:excel"
              xmlns="http://www.w3.org/TR/REC-html40">
        <head>
            <!--[if gte mso 9]>
            <xml>
                <x:ExcelWorkbook>
                    <x:ExcelWorksheets>
                        <x:ExcelWorksheet>
                            <x:Name>Daily Fee Report</x:Name>
                            <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
                        </x:ExcelWorksheet>
                    </x:ExcelWorksheets>
                </x:ExcelWorkbook>
            </xml>
            <![endif]-->
            <meta charset="UTF-8">
            <style>
                body { font-family: 'Inter', Arial, sans-serif; }
                table { width: 100%; border-collapse: collapse; margin: 15px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; font-size: 12px; }
                h3 { margin: 15px 0; }
            </style>
        </head>
        <body>
            ${summaryHtml}
            ${mainTable}
        </body>
        </html>
    `;

    // Create a Blob and trigger download
    const blob = new Blob([excelHtml], {
      type: "application/vnd.ms-excel"
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "Daily_Fee_Report.xls";
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }, 0);
  }

  $("#reportrange").daterangepicker({
    ranges: {
      'Today': [moment(), moment()],
      'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
      'Last 7 Days': [moment().subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().subtract(29, 'days'), moment()],
      'This Month': [moment().startOf('month'), moment().endOf('month')],
      // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'DD.MM.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()
  }, function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

  // Table search functionality
  $(document).on('keyup', '#table-search', function() {
    var value = $(this).val().toLowerCase();
    $("#customers2 tbody tr").filter(function() {
      $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
    });
  });
  $('#clear').on('click', function() {
    location.reload();
  });
</script>